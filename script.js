// Global variables for booking functionality
let selectedChairData = {};
let selectedTimeSlot = '';

// Wait for DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('ChairBooker website loaded successfully!');
});

// Home page functions
function joinWaitlist() {
    const name = prompt('Enter your name to join the waitlist:');
    if (name && name.trim()) {
        alert(`Thank you ${name}! You've been added to our waitlist. We'll contact you soon with updates about ChairBooker.`);
    }
}

function contactUs() {
    alert('Contact <NAME_EMAIL> or call (*************');
}

// Booking demo functions
function selectChair(chairNumber) {
    // Get chair element and barber name
    const chairElement = document.querySelector(`[data-chair="${chairNumber}"]`);
    const barberName = chairElement.querySelector('.barber-name').textContent;
    
    // Store selected chair data
    selectedChairData = {
        number: chairNumber,
        barber: barberName
    };
    
    // Update modal content
    document.getElementById('selectedChairNumber').textContent = chairNumber;
    document.getElementById('selectedBarberName').textContent = barberName;
    
    // Reset form
    resetBookingForm();
    
    // Show booking modal
    document.getElementById('bookingModal').style.display = 'block';
    
    // Add some visual feedback
    chairElement.style.transform = 'scale(1.05)';
    setTimeout(() => {
        chairElement.style.transform = '';
    }, 200);
}

function selectTime(time) {
    // Remove previous selection
    document.querySelectorAll('.time-slot').forEach(slot => {
        slot.classList.remove('selected');
    });
    
    // Add selection to clicked time slot
    event.target.classList.add('selected');
    
    // Store selected time
    selectedTimeSlot = time;
    document.getElementById('selectedTime').value = time;
}

function bookAppointment(event) {
    event.preventDefault();
    
    // Get form data
    const customerName = document.getElementById('customerName').value.trim();
    const customerPhone = document.getElementById('customerPhone').value.trim();
    
    // Validate form
    if (!customerName) {
        alert('Please enter your name.');
        return;
    }
    
    if (!customerPhone) {
        alert('Please enter your phone number.');
        return;
    }
    
    if (!selectedTimeSlot) {
        alert('Please select a time slot.');
        return;
    }
    
    // Validate phone number format (basic validation)
    const phoneRegex = /^[\d\s\-\(\)\+]+$/;
    if (!phoneRegex.test(customerPhone)) {
        alert('Please enter a valid phone number.');
        return;
    }
    
    // Close booking modal
    closeModal();
    
    // Show success modal with booking details
    showSuccessModal();
    
    // Mark chair as occupied (demo purposes)
    markChairAsOccupied(selectedChairData.number);
}

function showSuccessModal() {
    // Update success modal with booking details
    document.getElementById('confirmedChair').textContent = selectedChairData.number;
    document.getElementById('confirmedBarber').textContent = selectedChairData.barber;
    document.getElementById('confirmedTime').textContent = selectedTimeSlot;
    
    // Show success modal
    document.getElementById('successModal').style.display = 'block';
}

function markChairAsOccupied(chairNumber) {
    const chairElement = document.querySelector(`[data-chair="${chairNumber}"]`);
    chairElement.classList.remove('available');
    chairElement.classList.add('occupied');
    chairElement.onclick = null; // Remove click handler
    
    // Add a subtle animation
    chairElement.style.transition = 'all 0.5s ease';
    chairElement.style.opacity = '0.6';
}

function resetBookingForm() {
    // Clear form fields
    document.getElementById('customerName').value = '';
    document.getElementById('customerPhone').value = '';
    document.getElementById('selectedTime').value = '';
    
    // Clear time slot selection
    document.querySelectorAll('.time-slot').forEach(slot => {
        slot.classList.remove('selected');
    });
    
    // Reset selected time
    selectedTimeSlot = '';
}

function closeModal() {
    document.getElementById('bookingModal').style.display = 'none';
    resetBookingForm();
}

function closeSuccessModal() {
    document.getElementById('successModal').style.display = 'none';
}

// Close modals when clicking outside of them
window.onclick = function(event) {
    const bookingModal = document.getElementById('bookingModal');
    const successModal = document.getElementById('successModal');
    
    if (event.target === bookingModal) {
        closeModal();
    }
    
    if (event.target === successModal) {
        closeSuccessModal();
    }
}

// Handle escape key to close modals
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        closeModal();
        closeSuccessModal();
    }
});

// Smooth scrolling for anchor links (if any are added later)
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

// Add some interactive animations on scroll (optional enhancement)
function addScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);
    
    // Observe elements for animation
    document.querySelectorAll('.feature-card, .benefit-card, .gallery-item').forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(20px)';
        el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(el);
    });
}

// Initialize scroll animations when page loads
window.addEventListener('load', addScrollAnimations);

// Add loading state for buttons (enhancement)
function addLoadingState(button, duration = 1000) {
    const originalText = button.textContent;
    button.textContent = 'Loading...';
    button.disabled = true;
    
    setTimeout(() => {
        button.textContent = originalText;
        button.disabled = false;
    }, duration);
}

// Console welcome message
console.log(`
🪑 Welcome to ChairBooker! 
📧 Contact: <EMAIL>
🚀 This is a demo website showcasing our booking platform concept.
`);

// Demo data for development (can be removed in production)
const demoData = {
    salon: {
        name: "Premium Cuts Salon",
        chairs: [
            { id: 1, barber: "Mike", available: true },
            { id: 2, barber: "Sarah", available: false },
            { id: 3, barber: "Alex", available: true },
            { id: 4, barber: "Jordan", available: true },
            { id: 5, barber: "Taylor", available: false },
            { id: 6, barber: "Casey", available: true }
        ],
        timeSlots: ["10:00 AM", "11:30 AM", "1:00 PM", "2:30 PM", "4:00 PM", "5:30 PM"]
    }
};

// Export demo data for potential future use
window.ChairBookerDemo = demoData;
