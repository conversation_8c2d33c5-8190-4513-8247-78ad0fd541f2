// Global variables for booking functionality
let selectedChairData = {};
let selectedTimeSlot = '';

// Wait for DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('ChairBooker website loaded successfully!');

    // Load selected location if on booking page
    if (window.location.pathname.includes('booking.html')) {
        loadSelectedLocation();
    }
});

// Load selected location information
function loadSelectedLocation() {
    const selectedLocation = sessionStorage.getItem('selectedLocation');
    const selectedSalon = sessionStorage.getItem('selectedSalon');

    if (selectedLocation && selectedSalon) {
        const locationNameElement = document.getElementById('selectedLocationName');
        const salonNameElement = document.getElementById('selectedSalonName');

        if (locationNameElement && salonNameElement) {
            locationNameElement.textContent = selectedLocation;
            salonNameElement.textContent = selectedSalon;
        }
    } else {
        // If no location selected, redirect to book-now page
        if (window.location.pathname.includes('booking.html')) {
            window.location.href = 'book-now.html';
        }
    }
}

// Home page functions
function contactUs() {
    alert('Contact <NAME_EMAIL> or call (*************');
}

// Location selection function
function selectLocation(locationName, salonName) {
    // Store selected location in sessionStorage
    sessionStorage.setItem('selectedLocation', locationName);
    sessionStorage.setItem('selectedSalon', salonName);

    // Add visual feedback
    const locationCards = document.querySelectorAll('.location-card');
    locationCards.forEach(card => {
        if (card.onclick.toString().includes(locationName)) {
            card.style.transform = 'scale(0.95)';
            setTimeout(() => {
                card.style.transform = '';
            }, 150);
        }
    });

    // Navigate to booking page after short delay
    setTimeout(() => {
        window.location.href = 'booking.html';
    }, 300);
}

// Booking demo functions
function selectChair(chairNumber) {
    // Get chair element and barber name
    const chairElement = document.querySelector(`[data-chair="${chairNumber}"]`);
    const barberName = chairElement.querySelector('.barber-name').textContent;
    
    // Store selected chair data
    selectedChairData = {
        number: chairNumber,
        barber: barberName
    };
    
    // Update modal content
    document.getElementById('selectedChairNumber').textContent = chairNumber;
    document.getElementById('selectedBarberName').textContent = barberName;
    
    // Reset form
    resetBookingForm();
    
    // Update time slot availability
    updateTimeSlotAvailability(chairNumber);

    // Show booking modal
    document.getElementById('bookingModal').style.display = 'block';

    // Add some visual feedback
    chairElement.style.transform = 'scale(1.05)';
    setTimeout(() => {
        chairElement.style.transform = '';
    }, 200);
}

function selectTime(time, event) {
    // Get the clicked button
    const clickedButton = event ? event.target : document.querySelector(`[onclick="selectTime('${time}')"]`);

    // Check if the time slot is booked
    if (clickedButton.disabled || clickedButton.classList.contains('booked')) {
        return; // Don't allow selection of booked slots
    }

    // Remove previous selection
    document.querySelectorAll('.time-slot').forEach(slot => {
        slot.classList.remove('selected');
    });

    // Add selection to clicked time slot
    clickedButton.classList.add('selected');

    // Store selected time
    selectedTimeSlot = time;
    document.getElementById('selectedTime').value = time;
}

function bookAppointment(event) {
    event.preventDefault();
    
    // Get form data
    const customerName = document.getElementById('customerName').value.trim();
    const customerPhone = document.getElementById('customerPhone').value.trim();
    
    // Validate form
    if (!customerName) {
        alert('Please enter your name.');
        return;
    }
    
    if (!customerPhone) {
        alert('Please enter your phone number.');
        return;
    }
    
    if (!selectedTimeSlot) {
        alert('Please select a time slot.');
        return;
    }
    
    // Validate phone number format (basic validation)
    const phoneRegex = /^[\d\s\-\(\)\+]+$/;
    if (!phoneRegex.test(customerPhone)) {
        alert('Please enter a valid phone number.');
        return;
    }
    
    // Close booking modal
    closeModal();
    
    // Show success modal with booking details
    showSuccessModal();

    // Mark time slot as booked (demo purposes)
    markTimeSlotAsBooked(selectedChairData.number, selectedTimeSlot);
}

function showSuccessModal() {
    // Update success modal with booking details
    document.getElementById('confirmedChair').textContent = selectedChairData.number;
    document.getElementById('confirmedBarber').textContent = selectedChairData.barber;
    document.getElementById('confirmedTime').textContent = selectedTimeSlot;
    
    // Show success modal
    document.getElementById('successModal').style.display = 'block';
}

// Store booked time slots for each chair
let bookedTimeSlots = {
    // Demo data - some pre-booked time slots
    2: ["10:00 AM", "2:30 PM"], // Chair 2 has 10:00 AM and 2:30 PM booked
    5: ["11:30 AM", "4:00 PM"]  // Chair 5 has 11:30 AM and 4:00 PM booked
};

function markTimeSlotAsBooked(chairNumber, timeSlot) {
    // Initialize chair's booked slots if not exists
    if (!bookedTimeSlots[chairNumber]) {
        bookedTimeSlots[chairNumber] = [];
    }

    // Add the time slot to booked slots
    if (!bookedTimeSlots[chairNumber].includes(timeSlot)) {
        bookedTimeSlots[chairNumber].push(timeSlot);
    }

    console.log(`Time slot ${timeSlot} booked for Chair ${chairNumber}`);
}

function updateTimeSlotAvailability(chairNumber) {
    const timeSlotButtons = document.querySelectorAll('.time-slot');
    const chairBookedSlots = bookedTimeSlots[chairNumber] || [];

    console.log(`Updating availability for Chair ${chairNumber}:`, chairBookedSlots);

    timeSlotButtons.forEach(button => {
        const timeText = button.textContent.trim();

        // Reset button state
        button.classList.remove('booked', 'selected');
        button.disabled = false;
        button.style.cursor = 'pointer';

        // Check if this time slot is booked for this chair
        if (chairBookedSlots.includes(timeText)) {
            button.classList.add('booked');
            button.disabled = true;
            button.style.cursor = 'not-allowed';
            console.log(`Time slot ${timeText} is booked for Chair ${chairNumber}`);
        }
    });
}

function resetBookingForm() {
    // Clear form fields
    document.getElementById('customerName').value = '';
    document.getElementById('customerPhone').value = '';
    document.getElementById('selectedTime').value = '';

    // Clear time slot selection (but keep booked status)
    document.querySelectorAll('.time-slot').forEach(slot => {
        slot.classList.remove('selected');
        // Don't remove 'booked' class - that should persist
    });

    // Reset selected time
    selectedTimeSlot = '';
}

function closeModal() {
    document.getElementById('bookingModal').style.display = 'none';
    resetBookingForm();
}

function closeSuccessModal() {
    document.getElementById('successModal').style.display = 'none';
}

// Close modals when clicking outside of them
window.onclick = function(event) {
    const bookingModal = document.getElementById('bookingModal');
    const successModal = document.getElementById('successModal');
    
    if (event.target === bookingModal) {
        closeModal();
    }
    
    if (event.target === successModal) {
        closeSuccessModal();
    }
}

// Handle escape key to close modals
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        closeModal();
        closeSuccessModal();
    }
});

// Smooth scrolling for anchor links (if any are added later)
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

// Add some interactive animations on scroll (optional enhancement)
function addScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);
    
    // Observe elements for animation
    document.querySelectorAll('.feature-card, .benefit-card, .gallery-item').forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(20px)';
        el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(el);
    });
}

// Initialize scroll animations when page loads
window.addEventListener('load', addScrollAnimations);

// Add loading state for buttons (enhancement)
function addLoadingState(button, duration = 1000) {
    const originalText = button.textContent;
    button.textContent = 'Loading...';
    button.disabled = true;
    
    setTimeout(() => {
        button.textContent = originalText;
        button.disabled = false;
    }, duration);
}

// Console welcome message
console.log(`
🪑 Welcome to ChairBooker! 
📧 Contact: <EMAIL>
🚀 This is a demo website showcasing our booking platform concept.
`);

// Demo data for development (can be removed in production)
const demoData = {
    salon: {
        name: "Premium Cuts Salon",
        chairs: [
            { id: 1, barber: "Mike", available: true },
            { id: 2, barber: "Sarah", available: false },
            { id: 3, barber: "Alex", available: true },
            { id: 4, barber: "Jordan", available: true },
            { id: 5, barber: "Taylor", available: false },
            { id: 6, barber: "Casey", available: true }
        ],
        timeSlots: ["10:00 AM", "11:30 AM", "1:00 PM", "2:30 PM", "4:00 PM", "5:30 PM"]
    }
};

// Export demo data for potential future use
window.ChairBookerDemo = demoData;
