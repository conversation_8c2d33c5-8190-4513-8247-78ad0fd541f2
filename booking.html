<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ChairBooker - Booking Demo</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <h2>ChairBooker</h2>
            </div>
            <div class="nav-links">
                <a href="index.html">Home</a>
                <a href="book-now.html">Book Now</a>
            </div>
        </div>
    </nav>

    <!-- Booking Demo Section -->
    <section class="booking-demo">
        <div class="container">
            <div class="selected-location-info">
                <h1>Book Your Chair</h1>
                <div class="location-badge">
                    <span id="selectedLocationName">Damascus Downtown</span> - <span id="selectedSalonName">Premium Cuts Salon</span>
                </div>
            </div>
            <p class="demo-subtitle">Select a chair from our salon floor plan below</p>
            
            <!-- Salon Floor Plan -->
            <div class="salon-layout">
                <div class="salon-header">
                    <h3>Salon Floor Plan</h3>
                    <div class="legend">
                        <div class="legend-item">
                            <div class="legend-color available"></div>
                            <span>Available</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color occupied"></div>
                            <span>Occupied</span>
                        </div>
                    </div>
                </div>
                
                <div class="salon-grid">
                    <!-- Reception Area -->
                    <div class="reception-area">
                        <div class="reception-desk">Reception</div>
                    </div>
                    
                    <!-- Waiting Area -->
                    <div class="waiting-area">
                        <div class="waiting-seats">Waiting Area</div>
                    </div>
                    
                    <!-- Chair Grid -->
                    <div class="chairs-container">
                        <div class="chair available" data-chair="1" onclick="selectChair(1)">
                            <div class="chair-icon">🪑</div>
                            <div class="chair-number">Chair 1</div>
                            <div class="barber-name">Mike</div>
                        </div>
                        
                        <div class="chair available" data-chair="2" onclick="selectChair(2)">
                            <div class="chair-icon">🪑</div>
                            <div class="chair-number">Chair 2</div>
                            <div class="barber-name">Sarah</div>
                        </div>
                        
                        <div class="chair available" data-chair="3" onclick="selectChair(3)">
                            <div class="chair-icon">🪑</div>
                            <div class="chair-number">Chair 3</div>
                            <div class="barber-name">Alex</div>
                        </div>
                        
                        <div class="chair available" data-chair="4" onclick="selectChair(4)">
                            <div class="chair-icon">🪑</div>
                            <div class="chair-number">Chair 4</div>
                            <div class="barber-name">Jordan</div>
                        </div>
                        
                        <div class="chair available" data-chair="5" onclick="selectChair(5)">
                            <div class="chair-icon">🪑</div>
                            <div class="chair-number">Chair 5</div>
                            <div class="barber-name">Taylor</div>
                        </div>
                        
                        <div class="chair available" data-chair="6" onclick="selectChair(6)">
                            <div class="chair-icon">🪑</div>
                            <div class="chair-number">Chair 6</div>
                            <div class="barber-name">Casey</div>
                        </div>
                    </div>
                    
                    <!-- Wash Stations -->
                    <div class="wash-stations">
                        <div class="wash-station">Wash 1</div>
                        <div class="wash-station">Wash 2</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Booking Modal -->
    <div id="bookingModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Book Chair <span id="selectedChairNumber"></span></h2>
                <span class="close" onclick="closeModal()">&times;</span>
            </div>
            
            <div class="modal-body">
                <div class="barber-info">
                    <h3>Barber: <span id="selectedBarberName"></span></h3>
                </div>
                
                <form id="bookingForm">
                    <div class="form-group">
                        <label for="customerName">Your Name:</label>
                        <input type="text" id="customerName" name="customerName" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="customerPhone">Phone Number:</label>
                        <input type="tel" id="customerPhone" name="customerPhone" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="timeSlot">Available Times:</label>
                        <div class="time-slots">
                            <button type="button" class="time-slot" onclick="selectTime('10:00 AM', event)">10:00 AM</button>
                            <button type="button" class="time-slot" onclick="selectTime('11:30 AM', event)">11:30 AM</button>
                            <button type="button" class="time-slot" onclick="selectTime('1:00 PM', event)">1:00 PM</button>
                            <button type="button" class="time-slot" onclick="selectTime('2:30 PM', event)">2:30 PM</button>
                            <button type="button" class="time-slot" onclick="selectTime('4:00 PM', event)">4:00 PM</button>
                            <button type="button" class="time-slot" onclick="selectTime('5:30 PM', event)">5:30 PM</button>
                        </div>
                        <input type="hidden" id="selectedTime" name="selectedTime">
                    </div>
                    
                    <button type="submit" class="btn-book" onclick="bookAppointment(event)">Book Now</button>
                </form>
            </div>
        </div>
    </div>

    <!-- Success Modal -->
    <div id="successModal" class="modal">
        <div class="modal-content success-modal">
            <div class="modal-header">
                <h2>Booking Confirmed! ✅</h2>
                <span class="close" onclick="closeSuccessModal()">&times;</span>
            </div>
            
            <div class="modal-body">
                <div class="success-message">
                    <h3>Thank you! We'll contact you soon.</h3>
                    <p>Your appointment has been successfully booked.</p>
                    <div class="booking-details">
                        <p><strong>Chair:</strong> <span id="confirmedChair"></span></p>
                        <p><strong>Barber:</strong> <span id="confirmedBarber"></span></p>
                        <p><strong>Time:</strong> <span id="confirmedTime"></span></p>
                    </div>
                    <button class="btn-primary" onclick="closeSuccessModal()">Close</button>
                </div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
