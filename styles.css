/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Navigation */
.navbar {
    background: #fff;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
}

.nav-logo h2 {
    color: #4a90e2;
    font-size: 1.8rem;
}

.nav-links {
    display: flex;
    align-items: center;
    gap: 30px;
}

.nav-links a {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: color 0.3s;
}

.nav-links a:hover {
    color: #4a90e2;
}

/* Buttons */
.btn-primary, .btn-hero, .btn-secondary, .btn-book {
    padding: 12px 24px;
    border: none;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s;
    text-decoration: none;
    display: inline-block;
}

.btn-primary {
    background: #4a90e2;
    color: white;
}

.btn-primary:hover {
    background: #357abd;
    transform: translateY(-2px);
}

.btn-hero {
    background: #4a90e2;
    color: white;
    font-size: 1.1rem;
    padding: 15px 30px;
}

.btn-secondary {
    background: transparent;
    color: #4a90e2;
    border: 2px solid #4a90e2;
}

.btn-secondary:hover {
    background: #4a90e2;
    color: white;
}

.btn-book {
    background: #28a745;
    color: white;
    width: 100%;
    font-size: 1.1rem;
}

.btn-book:hover {
    background: #218838;
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 150px 0 100px;
    text-align: center;
}

.hero h1 {
    font-size: 3.5rem;
    margin-bottom: 20px;
    font-weight: 700;
}

.hero-tagline {
    font-size: 1.3rem;
    margin-bottom: 30px;
    opacity: 0.9;
}

/* Gallery Section */
.gallery {
    padding: 80px 0;
    background: white;
}

.gallery h2 {
    text-align: center;
    margin-bottom: 50px;
    font-size: 2.5rem;
    color: #333;
}

.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.gallery-item {
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: transform 0.3s;
}

.gallery-item:hover {
    transform: translateY(-5px);
}

.gallery-item img {
    width: 100%;
    height: 250px;
    object-fit: cover;
}

/* Location Section */
.location {
    padding: 80px 0;
    background: #f8f9fa;
}

.location h2 {
    text-align: center;
    margin-bottom: 50px;
    font-size: 2.5rem;
    color: #333;
}

.location-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 50px;
    align-items: center;
}

.location-text ul {
    list-style: none;
    margin-top: 20px;
}

.location-text li {
    padding: 8px 0;
    padding-left: 20px;
    position: relative;
}

.location-text li:before {
    content: "✓";
    position: absolute;
    left: 0;
    color: #4a90e2;
    font-weight: bold;
}

.map-container {
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

/* Features Section */
.features {
    padding: 80px 0;
    background: white;
}

.features h2 {
    text-align: center;
    margin-bottom: 50px;
    font-size: 2.5rem;
    color: #333;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.feature-card {
    background: white;
    padding: 40px 30px;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: transform 0.3s;
}

.feature-card:hover {
    transform: translateY(-5px);
}

.feature-icon {
    font-size: 3rem;
    margin-bottom: 20px;
}

.feature-card h3 {
    margin-bottom: 15px;
    color: #333;
}

/* Benefits Section */
.benefits {
    padding: 80px 0;
    background: #f8f9fa;
}

.benefits h2 {
    text-align: center;
    margin-bottom: 50px;
    font-size: 2.5rem;
    color: #333;
}

.benefits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.benefit-card {
    background: white;
    padding: 40px 30px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: transform 0.3s;
}

.benefit-card:hover {
    transform: translateY(-5px);
}

.benefit-card h3 {
    margin-bottom: 15px;
    color: #4a90e2;
}

/* Call to Action */
.cta {
    padding: 80px 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-align: center;
}

.cta h2 {
    font-size: 2.5rem;
    margin-bottom: 20px;
}

.cta p {
    font-size: 1.2rem;
    margin-bottom: 30px;
    opacity: 0.9;
}

.cta-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

/* Footer */
.footer {
    background: #333;
    color: white;
    padding: 50px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
    margin-bottom: 30px;
}

.footer-section h3 {
    margin-bottom: 20px;
    color: #4a90e2;
}

.social-icons {
    display: flex;
    gap: 15px;
    margin-top: 15px;
}

.social-icon {
    display: inline-block;
    font-size: 1.5rem;
    text-decoration: none;
    transition: transform 0.3s;
}

.social-icon:hover {
    transform: scale(1.2);
}

.footer-bottom {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid #555;
    opacity: 0.7;
}

/* Booking Demo Styles */
.booking-demo {
    padding: 100px 0 80px;
    background: white;
}

.booking-demo h1 {
    text-align: center;
    margin-bottom: 10px;
    font-size: 2.5rem;
    color: #333;
}

.demo-subtitle {
    text-align: center;
    margin-bottom: 50px;
    font-size: 1.2rem;
    color: #666;
}

/* Salon Layout */
.salon-layout {
    max-width: 800px;
    margin: 0 auto;
    background: white;
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 15px 40px rgba(0,0,0,0.1);
}

.salon-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid #f0f0f0;
}

.salon-header h3 {
    color: #333;
    font-size: 1.5rem;
}

.legend {
    display: flex;
    gap: 20px;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.legend-color {
    width: 20px;
    height: 20px;
    border-radius: 50%;
}

.legend-color.available {
    background: #28a745;
}

.legend-color.occupied {
    background: #dc3545;
}

/* Salon Grid Layout */
.salon-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: auto auto auto;
    gap: 20px;
    min-height: 400px;
}

.reception-area {
    grid-column: 1 / 2;
    grid-row: 1 / 2;
}

.waiting-area {
    grid-column: 2 / 3;
    grid-row: 1 / 2;
}

.chairs-container {
    grid-column: 1 / 3;
    grid-row: 2 / 3;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
}

.wash-stations {
    grid-column: 1 / 3;
    grid-row: 3 / 4;
    display: flex;
    gap: 20px;
}

/* Reception and Waiting Areas */
.reception-desk, .waiting-seats {
    background: #e9ecef;
    border-radius: 10px;
    padding: 20px;
    text-align: center;
    font-weight: 600;
    color: #666;
    border: 2px dashed #ccc;
}

/* Chair Styles */
.chair {
    background: white;
    border-radius: 15px;
    padding: 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s;
    border: 3px solid transparent;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.chair.available {
    border-color: #28a745;
}

.chair.available:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(40, 167, 69, 0.3);
}

.chair.occupied {
    border-color: #dc3545;
    opacity: 0.6;
    cursor: not-allowed;
}

.chair-icon {
    font-size: 2rem;
    margin-bottom: 10px;
}

.chair-number {
    font-weight: 600;
    margin-bottom: 5px;
    color: #333;
}

.barber-name {
    font-size: 0.9rem;
    color: #666;
}

/* Wash Stations */
.wash-station {
    flex: 1;
    background: #f8f9fa;
    border-radius: 10px;
    padding: 15px;
    text-align: center;
    font-weight: 600;
    color: #666;
    border: 2px solid #dee2e6;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    backdrop-filter: blur(5px);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 0;
    border-radius: 20px;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 20px 60px rgba(0,0,0,0.3);
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    padding: 25px 30px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h2 {
    color: #333;
    margin: 0;
}

.close {
    color: #aaa;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.3s;
}

.close:hover {
    color: #333;
}

.modal-body {
    padding: 30px;
}

.barber-info {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 10px;
    margin-bottom: 25px;
}

.barber-info h3 {
    color: #4a90e2;
    margin: 0;
}

/* Form Styles */
.form-group {
    margin-bottom: 25px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
}

.form-group input {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    font-size: 1rem;
    transition: border-color 0.3s;
}

.form-group input:focus {
    outline: none;
    border-color: #4a90e2;
}

/* Time Slots */
.time-slots {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 10px;
    margin-bottom: 15px;
}

.time-slot {
    padding: 12px 15px;
    border: 2px solid #e9ecef;
    background: white;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s;
    font-weight: 500;
}

.time-slot:hover {
    border-color: #4a90e2;
    background: #f8f9fa;
}

.time-slot.selected {
    background: #4a90e2;
    color: white;
    border-color: #4a90e2;
}

/* Success Modal */
.success-modal {
    text-align: center;
}

.success-message h3 {
    color: #28a745;
    margin-bottom: 15px;
}

.booking-details {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 10px;
    margin: 20px 0;
    text-align: left;
}

.booking-details p {
    margin-bottom: 10px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-links {
        gap: 15px;
    }

    .nav-links a {
        display: none;
    }

    .hero h1 {
        font-size: 2.5rem;
    }

    .location-content {
        grid-template-columns: 1fr;
    }

    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }

    .salon-grid {
        grid-template-columns: 1fr;
    }

    .chairs-container {
        grid-template-columns: repeat(2, 1fr);
    }

    .wash-stations {
        flex-direction: column;
    }

    .modal-content {
        margin: 10% auto;
        width: 95%;
    }

    .time-slots {
        grid-template-columns: repeat(2, 1fr);
    }
}
